-- MSSQL: Synchronize maxdate values for interaction job tables
-- This script ensures all interaction-related tables have consistent synchronization dates
-- to prevent data gaps after the implementation of individual table sync date tracking
-- Only runs for version 3.47.4 and excludes backfill tables
-- This script is automatically executed during installation/upgrade

-- Declare variables for the authoritative sync date
DECLARE @AuthoritativeSyncDate DATETIME;

-- Get the sync date from detailedinteractiondata as the authoritative source
SELECT @AuthoritativeSyncDate =
    CASE
        WHEN datekeyfield IS NULL OR datekeyfield = '' THEN CAST('2000-01-01 00:00:00' AS DATETIME)
        ELSE CAST(datekeyfield AS DATETIME)
    END
FROM tableDefinitions
WHERE tablename = 'detailedinteractiondata'
    AND version = '3.47.4';

-- Check if we found a valid authoritative sync date
IF @AuthoritativeSyncDate IS NULL
BEGIN
    PRINT 'No detailedinteractiondata found with version 3.47.4. Exiting script.';
    RETURN;
END

-- Display the authoritative sync date that will be used
PRINT 'Authoritative sync date from detailedinteractiondata (v3.47.4): ' + CONVERT(VARCHAR, @AuthoritativeSyncDate, 120);

-- Update interaction tables (excluding backfill) to use the detailedinteractiondata sync date
-- Only update if the target table's current maxdate is older than detailedinteractiondata
UPDATE tableDefinitions
SET datekeyfield = CONVERT(VARCHAR, @AuthoritativeSyncDate, 120)
WHERE tablename IN (
    'convsummarydata',
    'participantattributesdynamic',
    'participantsummarydata',
    'flowoutcomedata'
)
AND version = '3.47.4'
AND (
    datekeyfield IS NULL
    OR datekeyfield = ''
    OR CAST(datekeyfield AS DATETIME) < @AuthoritativeSyncDate
);

-- Display the reference date from detailedinteractiondata
SELECT
    'detailedinteractiondata' as reference_table,
    datekeyfield as reference_date,
    'This date was used for all interaction tables' as description
FROM tableDefinitions
WHERE tablename = 'detailedinteractiondata';

-- Display the synchronized dates for verification (only non-backfill tables)
SELECT
    tablename,
    datekeyfield as synchronized_date,
    version,
    'Interaction table sync dates synchronized to detailedinteractiondata' as status
FROM tableDefinitions
WHERE tablename IN (
    'detailedinteractiondata',
    'convsummarydata',
    'participantattributesdynamic',
    'participantsummarydata',
    'flowoutcomedata'
)
AND version = '3.47.4'
ORDER BY tablename;

PRINT 'Interaction table sync dates have been synchronized to detailedinteractiondata (v3.47.4) successfully.';

-- Optional: Set a specific sync date for detailedinteractiondata first, then sync others (uncomment and modify as required)
/*
DECLARE @SpecificSyncDate DATETIME = '2024-01-01 00:00:00';

-- First update detailedinteractiondata
UPDATE tableDefinitions
SET datekeyfield = CONVERT(VARCHAR, @SpecificSyncDate, 120)
WHERE tablename = 'detailedinteractiondata';

-- Then update all other interaction tables to match detailedinteractiondata
UPDATE tableDefinitions
SET datekeyfield = CONVERT(VARCHAR, @SpecificSyncDate, 120)
WHERE tablename IN (
    'detailedinteractiondata_backfill',
    'convsummarydata',
    'convsummarydata_backfill',
    'participantattributesdynamic',
    'participantattributesdynamic_backfill',
    'participantsummarydata',
    'participantsummarydata_backfill',
    'flowoutcomedata',
    'flowoutcomedata_backfill'
);

PRINT 'All interaction table sync dates set to specific date: ' + CONVERT(VARCHAR, @SpecificSyncDate, 120);
*/
