-- Snowflake: Synchronize maxdate values for interaction job tables
-- This script ensures all interaction-related tables have consistent synchronization dates
-- to prevent data gaps after the implementation of individual table sync date tracking
-- Only runs for version 3.47.4 and excludes backfill tables
-- This script is automatically executed during installation/upgrade

-- Create a temporary table to store the authoritative sync date from detailedinteractiondata
CREATE OR REPLACE TEMPORARY TABLE temp_authoritative_sync_date AS
SELECT
    CASE
        WHEN datekeyfield IS NULL OR datekeyfield = '' THEN TO_VARCHAR(TO_TIMESTAMP('2000-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD HH24:MI:SS')
        ELSE datekeyfield
    END as reference_date
FROM tableDefinitions
WHERE tablename = 'detailedinteractiondata'
    AND version = '3.47.4';

-- Display the authoritative sync date that will be used
SELECT
    reference_date as authoritative_sync_date_found,
    'This date from detailedinteractiondata will be used to synchronize all interaction tables' as description
FROM temp_authoritative_sync_date;

-- Update interaction tables (excluding backfill) to use the detailedinteractiondata sync date
-- Only update if the target table's current maxdate is older than detailedinteractiondata
UPDATE tableDefinitions
SET datekeyfield = (
    SELECT reference_date
    FROM temp_authoritative_sync_date
)
WHERE tablename IN (
    'convsummarydata',
    'participantattributesdynamic',
    'participantsummarydata',
    'flowoutcomedata'
)
AND version = '3.47.4'
AND EXISTS (SELECT 1 FROM temp_authoritative_sync_date WHERE reference_date IS NOT NULL)
AND (
    datekeyfield IS NULL
    OR datekeyfield = ''
    OR TO_TIMESTAMP(datekeyfield, 'YYYY-MM-DD HH24:MI:SS') < TO_TIMESTAMP((SELECT reference_date FROM temp_authoritative_sync_date), 'YYYY-MM-DD HH24:MI:SS')
);

-- Display the reference date from detailedinteractiondata
SELECT
    'detailedinteractiondata' as reference_table,
    datekeyfield as reference_date,
    'This date was used for all interaction tables' as description
FROM tableDefinitions
WHERE tablename = 'detailedinteractiondata';

-- Display the synchronized dates for verification (only non-backfill tables)
SELECT
    tablename,
    datekeyfield as synchronized_date,
    version,
    'Interaction table sync dates synchronized to detailedinteractiondata' as status
FROM tableDefinitions
WHERE tablename IN (
    'detailedinteractiondata',
    'convsummarydata',
    'participantattributesdynamic',
    'participantsummarydata',
    'flowoutcomedata'
)
AND version = '3.47.4'
ORDER BY tablename;

-- Clean up temporary table
DROP TABLE temp_authoritative_sync_date;

-- Optional: Set a specific sync date for detailedinteractiondata first, then sync others (uncomment and modify as required)
/*
-- First update detailedinteractiondata
UPDATE tableDefinitions
SET datekeyfield = '2024-01-01 00:00:00'
WHERE tablename = 'detailedinteractiondata';

-- Then update all other interaction tables to match detailedinteractiondata
UPDATE tableDefinitions
SET datekeyfield = '2024-01-01 00:00:00'
WHERE tablename IN (
    'detailedinteractiondata_backfill',
    'convsummarydata',
    'convsummarydata_backfill',
    'participantattributesdynamic',
    'participantattributesdynamic_backfill',
    'participantsummarydata',
    'participantsummarydata_backfill',
    'flowoutcomedata',
    'flowoutcomedata_backfill'
);

SELECT 'All interaction table sync dates set to specific date: 2024-01-01 00:00:00' as result;
*/
