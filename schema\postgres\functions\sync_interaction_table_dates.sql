-- PostgreSQL: Synchronize maxdate values for interaction job tables
-- This script ensures all interaction-related tables have consistent synchronization dates
-- to prevent data gaps after the implementation of individual table sync date tracking
-- Only runs for version 3.47.4 and excludes backfill tables
-- This script is automatically executed during installation/upgrade

-- Get the sync date from detailedinteractiondata as the authoritative source
WITH authoritative_sync_date AS (
    SELECT
        CASE
            WHEN datekeyfield IS NULL THEN '2000-01-01 00:00:00'::timestamp
            ELSE datekeyfield
        END as reference_date
    FROM tabledefinitions
    WHERE tablename = 'detailedinteractiondata'
        AND version = '3.47.4'
)
-- Update interaction tables (excluding backfill) to use the detailedinteractiondata sync date
-- Only update if the target table's current maxdate is older than detailedinteractiondata
UPDATE tabledefinitions
SET datekeyfield = (SELECT reference_date FROM authoritative_sync_date)
WHERE tablename IN (
    'convsummarydata',
    'participantattributesdynamic',
    'participantsummarydata',
    'flowoutcomedata'
)
AND version = '3.47.4'
AND EXISTS (SELECT 1 FROM authoritative_sync_date)
AND (
    datekeyfield IS NULL
    OR datekeyfield < (SELECT reference_date FROM authoritative_sync_date)
);

-- Display the reference date from detailedinteractiondata
SELECT
    'detailedinteractiondata' as reference_table,
    datekeyfield as reference_date,
    'This date will be used for all interaction tables' as description
FROM tabledefinitions
WHERE tablename = 'detailedinteractiondata';

-- Display the synchronized dates for verification (only non-backfill tables)
SELECT
    tablename,
    datekeyfield as synchronized_date,
    version,
    'Interaction table sync dates synchronized to detailedinteractiondata' as status
FROM tabledefinitions
WHERE tablename IN (
    'detailedinteractiondata',
    'convsummarydata',
    'participantattributesdynamic',
    'participantsummarydata',
    'flowoutcomedata'
)
AND version = '3.47.4'
ORDER BY tablename;

-- Optional: Set a specific sync date if needed (uncomment and modify as required)
-- UPDATE tabledefinitions 
-- SET datekeyfield = '2024-01-01 00:00:00'::timestamp
-- WHERE tablename IN (
--     'detailedinteractiondata',
--     'detailedinteractiondata_backfill',
--     'convsummarydata', 
--     'convsummarydata_backfill',
--     'participantattributesdynamic',
--     'participantattributesdynamic_backfill',
--     'participantsummarydata',
--     'participantsummarydata_backfill',
--     'flowoutcomedata',
--     'flowoutcomedata_backfill'
-- );
